import { useState } from "react";
import { <PERSON>Chart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Responsive<PERSON><PERSON><PERSON>, <PERSON> } from "recharts";
import DatePicker from "react-datepicker";
import { useGetAllInvestmentsQuery } from "@/redux/services/statsApiSlice";
import { extractDMY, normalizeDate, prepareOneWeekInterval } from "@/util/dateHelpers";

const InvestmentsChart = () => {
  const [dateRange, setDateRange] = useState({
    fromDate: "2025-01-01",
    toDate: "2025-12-31"
  });

  // Format dates for API and use correct field names
  const apiParams = fromDate && toDate ? {
    start_date: extractDMY(fromDate),
    end_date: extractDMY(toDate)
  } : {};

  const {data, isLoading, isFetching, isError, error } = useGetAllInvestmentsQuery(apiParams, {
    skip: !fromDate || !toDate,
  });

  // Transform backend data for line chart - group by month and sum values
  const transformDataForChart = (backendData) => {
    if (!backendData) return [];

    const investments = backendData.INVESTMENT || {};
    const withdraws = backendData.WITHDRAW || {};
    const monthNames = ["Yan", "Fev", "Mar", "Apr", "May", "İyn", "İyl", "Avq", "Sen", "Okt", "Noy", "Dek"];
    const monthlyData = {};

    // Get all unique dates
    const allDates = [...new Set([...Object.keys(investments), ...Object.keys(withdraws)])];

    // Group data by month and sum values
    allDates.forEach(date => {
      const dateObj = new Date(date.split('-').reverse().join('-')); // Convert DD-MM-YYYY to YYYY-MM-DD
      const monthYear = `${dateObj.getFullYear()}-${dateObj.getMonth()}`; // Create unique month-year key
      const monthName = monthNames[dateObj.getMonth()];

      if (!monthlyData[monthYear]) {
        monthlyData[monthYear] = {
          name: monthName,
          əlavələr: 0,
          çəkimlər: 0,
          year: dateObj.getFullYear(),
          month: dateObj.getMonth()
        };
      }

      monthlyData[monthYear].əlavələr += investments[date] || 0;
      monthlyData[monthYear].çəkimlər += withdraws[date] || 0;
    });

    // Convert to array and sort by date
    return Object.values(monthlyData).sort((a, b) => {
      if (a.year !== b.year) return a.year - b.year;
      return a.month - b.month;
    }).map(item => ({
      name: item.name,
      əlavələr: item.əlavələr,
      çəkimlər: item.çəkimlər
    }));
  };

  const chartData = transformDataForChart(data);

  // Format large numbers for Y-axis display
  const formatYAxisValue = (value) => {
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(0)}K`;
    }
    return value.toString();
  };

  return (
    <div className="p-4 rounded-lg shadow bg-white">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold">İnvestisiyalar</h2>
        <div className="w-64">
          <DatePicker
            selectsRange
            startDate={dateRange[0]}
            endDate={dateRange[1]}
            onChange={update => {
              if (Array.isArray(update)) {
                setDateRange(
                  update.map(d => (d ? normalizeDate(d) : null))
                );
              } else {
                setDateRange(update);
              }
            }}
            isClearable
            showWeekNumbers
            className="w-full text-[14px] placeholder:text-[#666666] outline-none"
          />
        </div>
      </div>
      <div style={{ height: 180 }}>
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={chartData} margin={{ top: 5, right: 20, bottom: 5, left: 0 }}>
            <CartesianGrid strokeDasharray="3 3" horizontal={true} vertical={false} />
            <XAxis dataKey="name" axisLine={false} tickLine={false} />
            <YAxis axisLine={false} tickLine={false} tickFormatter={formatYAxisValue} />
            <Tooltip formatter={(value, name) => [`₼${value.toLocaleString()}`, name]} />
            <Legend />
            <Line 
              type="monotone" 
              dataKey="əlavələr" 
              stroke="#4CAF50" 
              strokeWidth={2} 
              dot={false} 
              activeDot={false} 
            />
            <Line 
              type="monotone" 
              dataKey="çəkimlər" 
              stroke="#F44336" 
              strokeWidth={2} 
              dot={false} 
              activeDot={false} 
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default InvestmentsChart;
