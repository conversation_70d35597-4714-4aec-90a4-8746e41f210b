import { Helmet } from "react-helmet-async";
import SalesChart from "@/components/Dashboard/Homepage/SalesChart";
import CostsChart from "@/components/Dashboard/Homepage/CostsChart";
import InvestmentsChart from "@/components/Dashboard/Homepage/InvestmentsChart";
import InstallmentsChart from "@/components/Dashboard/Homepage/InstallmentsChart";
// import DelaysChart from "@/components/Dashboard/Homepage/DelaysChart";
import MonthlyIncomeChart from "@/components/Dashboard/Homepage/MonthlyIncomeChart";



function Homepage() {
  return (
    <>
      <Helmet>
        <title>inKredo | Ana Səhifə</title>
      </Helmet>
      <div className="py-6 pr-6 w-full h-[calc(100vh - 24px)] grid grid-cols-1 gap-6 lg:grid-cols-2 xl:grid-cols-3">
        <SalesChart />
        <CostsChart />
        <InvestmentsChart />
        <InstallmentsChart />
        {/* <DelaysChart /> */}
        <MonthlyIncomeChart />
      </div>
    </>
  )
}

export default Homepage