import { getDayOfYear } from "date-fns";
import { MONTH_NAMES, WEEK_DAY_NAMES } from "./constants";
import { extractDMY } from "./dateHelpers";

export function objectFormatter(obj) {
  return Object.fromEntries(
    Object.entries(obj).map(([key, value]) => [
      key,
      value === "" ? null : value,
    ])
  );
}

export function searchDataRefractorer(obj) {
  return Object.fromEntries(
    Object.entries(obj).filter(([key, value]) => value)
  );
}

export function roundTo2(num) {
  return Math.round(num * 100) / 100;
}

export function formatBalanceData(balance) {
  return {
    assetsData: [
      {
        No: "1",
        title: "Kassa",
        value: balance.assets.cash,
        propertyName: "money_pool_available_amount",
        canOpenModal: true,
      },
      {
        No: "3",
        title: "Alacaqlar",
        value: balance.assets.receivables,
        canOpenModal: false,
      },
      {
        No: "4",
        title: "Ticarət Malları",
        value: balance.assets.trade_goods,
        canOpenModal: false,
      },
      {
        No: "8",
        title: "Gələcək dövrün xərcləri",
        value: balance.assets.future_period_expenses,
        canOpenModal: false,
      },
    ],
    liabilityData: [
      {
        No: "5",
        title: "Adil Finans",
        value: balance.liabilities.company_balance,
        propertyName: "company_balance",
        canOpenModal: true,
      },
      {
        No: "5.1.1",
        title: "Şirkətin bölünməmiş mənfəəti",
        value: balance.liabilities.retained_earnings_of_the_company,
        propertyName: "retained_earnings_of_the_company",
        canOpenModal: true,
      },
      {
        No: "6",
        title: "Hövzə",
        value: balance.liabilities.money_pool,
        propertyName: "money_pool_capacity_amount",
        canOpenModal: true,
      },
      {
        No: "6.1",
        title: "Hövzə bölünməmiş mənfəət",
        value: balance.liabilities.unallocated_profit_of_the_money_pool,
        propertyName: "unallocated_profit_of_the_money_pool",
        canOpenModal: true,
      },
      {
        No: "7",
        title: "Komissiya",
        value: balance.liabilities.commission,
        canOpenModal: false,
      },
      {
        No: "7.1",
        title: "Xərc",
        propertyName: "commission",
        value: balance.liabilities.cost,
        canOpenModal: true,
      },
      {
        No: "9",
        title: "Gələcək dövrün mənfəəti",
        value: balance.liabilities.future_period_profit,
        canOpenModal: false,
      },
    ],
  };
}

export const transformText = (data, text) => {
  if (
    data.operation_type === "INVESTMENT" ||
    data.operation_type === "WITHDRAW" ||
    data.operation_type === "TRANSFER" ||
    data.operation_type === "SALE" ||
    data.operation_type === "USER_UPDATE" ||
    data.operation_type === "CREATE_WALLET" ||
    data.operation_type === "USER_CREATE"
  ) {
    let startIndex = text.indexOf(data?.user?.fullname);
    let linkText = data?.user?.fullname;
    let linkTo = `/sales/users/${data?.user?.id}`;
    let type = "investor";

    if (startIndex === -1 && data?.user?.fullname) {
      const nameParts = data.user.fullname.split(" ");
      if (nameParts.length > 0) {
        const firstName = nameParts[0];
        startIndex = text.indexOf(firstName);
        if (startIndex !== -1) {
          linkText = firstName;
        }
      }
    }

    if (
      startIndex === -1 &&
      data.operation_type === "SALE" &&
      data.contract?.customer?.fullname
    ) {
      const customerName = data.contract.customer.fullname;
      startIndex = text.indexOf(customerName);
      linkText = customerName;
      linkTo = `/sales/users/${data.contract.customer.id}`;
      type = "investor";

      if (startIndex === -1) {
        const nameParts = customerName.split(" ");
        if (nameParts.length > 0) {
          const firstName = nameParts[0];
          startIndex = text.indexOf(firstName);
          if (startIndex !== -1) {
            linkText = firstName;
          }
        }
      }
    }

    if (startIndex !== -1) {
      return {
        type,
        before: text.substring(0, startIndex),
        linkText,
        linkTo,
        after: text.substring(startIndex + linkText.length),
      };
    }
  } else if (
    data.operation_type === "CREDIT_PAYMENT" ||
    data.operation_type === "UPDATE_INSTALLMENT" ||
    data.operation_type === "CONTRACT_UPDATE"
  ) {
    const afIdRegex = /AF\d+/;
    const match = text.match(afIdRegex);

    if (match && match[0] && data.contract?.id) {
      const contractId = match[0];
      const parts = text.split(contractId);

      return {
        type: "contract",
        before: parts[0],
        linkText: contractId,
        linkTo: `/sales/contracts/${data.contract.id}`,
        after: parts[1] || "",
      };
    }
  }

  return { type: "plain", text };
};

/**
 * Transforms backend data for chart display based on the selected range type.
 *
 * @param {Record<string, number>} backendData - The raw data received from the backend.
 * @param {"WEEK" | "MONTH" | "YEAR"} rangeType - The type of range selected (e.g., 'WEEK', 'MONTH', 'YEAR').
 * @param {{ fromDate: Date, toDate: Date }} dateRange - The date range object with start and end dates.
 * @returns {{name: string, value: number}} Transformed data suitable for charting.
 */

export const transformDataForChart = (backendData, rangeType, dateRange) => {
  if (!backendData) return [];

  const { fromDate, toDate } = dateRange;
  const chartData = {};

  if (rangeType === "YEAR") {
    const currentDate = new Date(fromDate);

    while (currentDate <= toDate) {
      const month = currentDate.getMonth();
      const year = currentDate.getFullYear();
      const key = `${month}-${year}`;

      if (!chartData[key]) {
        chartData[key] = {
          name: MONTH_NAMES[month],
          value: 0,
        };
      }

      currentDate.setMonth(currentDate.getMonth() + 1);
    }

    Object.keys(backendData).forEach((date) => {
      const dateObj = new Date(date.split("-").reverse().join("-"));
      const month = dateObj.getMonth();
      const year = dateObj.getFullYear();
      const key = `${month}-${year}`;

      if (chartData[key]) {
        chartData[key].value += backendData[date];
      }
    });
  } else if (rangeType === "MONTH") {
    const currentDate = new Date(fromDate);

    while (currentDate <= toDate) {
      const day = currentDate.getDate();
      const month = currentDate.getMonth();
      const key = currentDate.toDateString();

      chartData[key] = {
        name: `${day} ${MONTH_NAMES[month]}`,
        value: 0,
      };

      currentDate.setDate(currentDate.getDate() + 1);
    }

    console.log(chartData);

    Object.keys(backendData).forEach((date) => {
      const dateObj = new Date(date.split("-").reverse().join("-"));
      const key = dateObj.toDateString();

      if (chartData[key]) {
        chartData[key].value += backendData[date];
      }
    });
  } else {
    const currentDate = new Date(fromDate);

    while (currentDate <= toDate) {
      const key = currentDate.toDateString();

      chartData[key] = {
        name: `${currentDate.getDate()} - ${
          WEEK_DAY_NAMES[currentDate.getDay()]
        }`,
        value: 0,
      };

      currentDate.setDate(currentDate.getDate() + 1);
    }

    Object.keys(backendData).forEach((date) => {
      const dateObj = new Date(date.split("-").reverse().join("-"));
      const key = dateObj.toDateString();

      if (chartData[key]) {
        chartData[key].value += backendData[date];
      }
    });
  }

  return Object.entries(chartData).map(([_, value]) => value);
};

export function prepareChartData(
  allRaw,
  previousRange,
  currentRange,
  rangeType
) {
  const { previousFromDate, previousToDate } = previousRange;
  const { fromDate, toDate } = currentRange;

  const previousSeries =
    allRaw && previousFromDate && previousToDate
      ? transformDataForChart(allRaw, rangeType, {
          fromDate: previousFromDate,
          toDate: previousToDate,
        })
      : [];

  const currentSeries =
    allRaw && fromDate && toDate
      ? transformDataForChart(allRaw, rangeType, { fromDate, toDate })
      : [];

  const mergedChartData = currentSeries.map((curr, idx) => ({
    name: curr.name,
    previousName: previousSeries[idx]?.name || "",
    value: curr.value,
    previousValue: previousSeries[idx]?.value || 0,
  }));

  const maxValue =
    currentSeries.length > 0
      ? Math.max(...currentSeries.map((item) => item.value))
      : 0;
  const yAxisMax = Math.ceil(maxValue * 1.1);

  const total = allRaw
    ? currentSeries.reduce((sum, item) => sum + (item.value || 0), 0)
    : 0;
  const roundedTotal = roundTo2(total);

  return {
    previousSeries,
    currentSeries,
    mergedChartData,
    yAxisMax,
    roundedTotal,
  };
}

export const formatYAxisValue = (value) => {
  if (value >= 1_000_000_000) return `${(value / 1_000_000_000).toFixed(1)}B`;
  if (value >= 1_000_000) return `${(value / 1_000_000).toFixed(1)}M`;
  if (value >= 1000) return `${(value / 1000).toFixed(0)}K`;
  return value.toString();
};

export function preparePreviousDateRange(rangeType, fromDate, toDate) {
  let previousFromDate, previousToDate;
  if (rangeType === "WEEK") {
    previousFromDate = new Date(fromDate);
    previousFromDate.setDate(previousFromDate.getDate() - 7);

    previousToDate = new Date(toDate);
    previousToDate.setDate(previousToDate.getDate() - 7);
  } else if (rangeType === "MONTH") {
    previousFromDate = new Date(fromDate);
    previousFromDate.setMonth(previousFromDate.getMonth() - 1);

    previousToDate = new Date(toDate);
    previousToDate.setMonth(previousToDate.getMonth() - 1);
  } else if (rangeType === "YEAR") {
    previousFromDate = new Date(fromDate);
    previousFromDate.setFullYear(previousFromDate.getFullYear() - 1);

    previousToDate = new Date(toDate);
    previousToDate.setFullYear(previousToDate.getFullYear() - 1);
  }

  return { previousFromDate, previousToDate };
}

export function prepareChartRequestParams(fromDate, toDate) {
  return fromDate && toDate
    ? {
        start_date: extractDMY(fromDate),
        end_date: extractDMY(toDate),
      }
    : {};
}
